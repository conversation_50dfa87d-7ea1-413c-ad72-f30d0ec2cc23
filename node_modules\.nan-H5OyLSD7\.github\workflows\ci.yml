# https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-nodejs
# https://github.com/actions/setup-node
# https://docs.github.com/en/actions/using-github-hosted-runners/using-github-hosted-runners/about-github-hosted-runners#standard-github-hosted-runners-for-public-repositories

name: ci
on:
  push:
  #  branches: [main]
  pull_request:
  #  branches: [main]
  workflow_dispatch:
permissions:
  contents: read
jobs:
  ci:
    strategy:
      fail-fast: false
      matrix:  # TODO: Enable 23.x after nodejs/nan#979 or similar.
        node-version: [22.x, 21.x, 20.x, 19.x, 18.x, 17.x, 16.x]
        os: [windows-latest]
        include:
          - node-version: lts/*
            os: macos-13  # macOS on Intel
          - node-version: lts/*
            os: macos-latest  # macOS on arm64
          - node-version: lts/*
            os: ubuntu-latest  # Linux on x64
          - node-version: lts/*
            os: ubuntu-24.04-arm  # Linux on arm64
          - node-version: lts/*
            os: windows-2025
          - node-version: 14.x
            os: windows-2019
          - node-version: 12.x
            os: windows-2019
          - node-version: 10.x
            os: windows-2019
          - node-version: 8.x
            os: windows-2019
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
      - run: npm install
      # TODO: On Windows Node.js v11 these will fail but `make test` will succeed
      - if: matrix.node-version != '11.x'
        run: |
          npm run-script rebuild-tests
          npm test
      - run: make test
